#!/usr/bin/env python3
"""
🚀 Football News Bot - Ultimate Launcher
مشغل وكيل أخبار كرة القدم المتطور

This is the ultimate launcher that handles everything automatically:
- Checks and installs missing packages
- Verifies system health
- Handles authentication automatically
- Manages service files
- Provides beautiful interface
"""

import os
import sys
import subprocess
import json
import time
import asyncio
from pathlib import Path
from datetime import datetime

# Colors for beautiful output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_banner():
    """Print beautiful banner"""
    banner = f"""
{Colors.HEADER}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║        🚀 Football News Bot - Ultimate Launcher 🚀          ║
║                                                              ║
║              مشغل وكيل أخبار كرة القدم المتطور              ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
{Colors.ENDC}

{Colors.OKCYAN}🔥 المميزات الرهيبة:{Colors.ENDC}
{Colors.OKGREEN}✅ فحص وتثبيت المكتبات تلقائياً{Colors.ENDC}
{Colors.OKGREEN}✅ إدارة المصادقة التلقائية{Colors.ENDC}
{Colors.OKGREEN}✅ فحص صحة النظام الشامل{Colors.ENDC}
{Colors.OKGREEN}✅ إدارة ملفات الخدمة{Colors.ENDC}
{Colors.OKGREEN}✅ واجهة جميلة وسهلة{Colors.ENDC}

{Colors.WARNING}⚡ جاري التحقق من النظام...{Colors.ENDC}
"""
    print(banner)

def log_step(step, message, status="info"):
    """Log step with beautiful formatting"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    
    if status == "success":
        color = Colors.OKGREEN
        icon = "✅"
    elif status == "error":
        color = Colors.FAIL
        icon = "❌"
    elif status == "warning":
        color = Colors.WARNING
        icon = "⚠️"
    else:
        color = Colors.OKBLUE
        icon = "🔍"
    
    print(f"{color}[{timestamp}] {icon} {step}: {message}{Colors.ENDC}")

def check_python_version():
    """Check Python version"""
    log_step("Python Version", "Checking Python version...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        log_step("Python Version", f"Python {version.major}.{version.minor} detected. Need Python 3.8+", "error")
        return False
    
    log_step("Python Version", f"Python {version.major}.{version.minor}.{version.micro} ✅", "success")
    return True

def check_and_install_packages():
    """Check and install required packages"""
    log_step("Packages", "Checking required packages...")
    
    required_packages = [
        "python-dotenv",
        "google-api-python-client",
        "google-auth-httplib2", 
        "google-auth-oauthlib",
        "selenium",
        "webdriver-manager",
        "beautifulsoup4",
        "requests",
        "google-generativeai",
        "python-telegram-bot>=20.0",
        "Markdown",
        "Pillow"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            # Extract package name (remove version requirements)
            pkg_name = package.split('>=')[0].split('==')[0]
            __import__(pkg_name.replace('-', '_'))
            log_step("Package Check", f"{pkg_name} ✅", "success")
        except ImportError:
            missing_packages.append(package)
            log_step("Package Check", f"{pkg_name} ❌", "error")
    
    if missing_packages:
        log_step("Installation", f"Installing {len(missing_packages)} missing packages...", "warning")
        
        for package in missing_packages:
            try:
                log_step("Installing", f"Installing {package}...")
                result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    log_step("Installation", f"{package} installed ✅", "success")
                else:
                    log_step("Installation", f"Failed to install {package}: {result.stderr}", "error")
                    return False
            except Exception as e:
                log_step("Installation", f"Error installing {package}: {e}", "error")
                return False
    
    log_step("Packages", "All packages are ready! ✅", "success")
    return True

def check_env_file():
    """Check and create .env file"""
    log_step("Environment", "Checking .env file...")
    
    if not os.path.exists('.env'):
        log_step("Environment", "Creating .env file from template...", "warning")
        
        if os.path.exists('env.example'):
            try:
                with open('env.example', 'r', encoding='utf-8') as src:
                    content = src.read()
                with open('.env', 'w', encoding='utf-8') as dst:
                    dst.write(content)
                log_step("Environment", ".env file created ✅", "success")
            except Exception as e:
                log_step("Environment", f"Failed to create .env: {e}", "error")
                return False
        else:
            log_step("Environment", "env.example not found", "error")
            return False
    
    log_step("Environment", ".env file exists ✅", "success")
    return True

def check_service_files():
    """Check and create service files"""
    log_step("Service Files", "Checking service files...")
    
    service_files = [
        "telegram_control_bot.py",
        "system_health_checker.py", 
        "blogger_auth_telegram.py",
        "main.py"
    ]
    
    missing_files = []
    for file in service_files:
        if not os.path.exists(file):
            missing_files.append(file)
            log_step("Service Check", f"{file} ❌", "error")
        else:
            log_step("Service Check", f"{file} ✅", "success")
    
    if missing_files:
        log_step("Service Files", f"Missing {len(missing_files)} service files", "error")
        return False
    
    log_step("Service Files", "All service files present ✅", "success")
    return True

async def check_system_health():
    """Check system health"""
    log_step("Health Check", "Running comprehensive system health check...")
    
    try:
        from system_health_checker import health_checker
        
        health_ok = await health_checker.run_full_health_check()
        report = health_checker.get_health_report()
        
        if health_ok:
            log_step("Health Check", "System is healthy ✅", "success")
        else:
            log_step("Health Check", "System has issues ⚠️", "warning")
            
            # Show specific issues
            for error in report['errors'][:3]:
                log_step("Health Issue", error, "warning")
        
        return health_ok, report
        
    except Exception as e:
        log_step("Health Check", f"Health check failed: {e}", "error")
        return False, {}

async def auto_authenticate_blogger():
    """Automatically handle Blogger authentication"""
    log_step("Authentication", "Checking Blogger authentication...")
    
    try:
        from blogger_auth_telegram import BloggerAuthTelegram
        
        auth_manager = BloggerAuthTelegram()
        credentials = await auth_manager.load_credentials_from_env()
        
        if credentials and credentials.valid:
            log_step("Authentication", "Blogger authentication valid ✅", "success")
            return True
        else:
            log_step("Authentication", "Blogger authentication needed ⚠️", "warning")
            
            # Check if we have client_secret.json for auto-auth
            if os.path.exists('client_secret.json'):
                log_step("Auto-Auth", "client_secret.json found, attempting auto-authentication...", "warning")
                
                # Here you could implement automatic authentication
                # For now, we'll just inform the user
                log_step("Auto-Auth", "Please use Telegram bot for authentication", "warning")
                return False
            else:
                log_step("Auto-Auth", "client_secret.json not found", "error")
                return False
                
    except Exception as e:
        log_step("Authentication", f"Authentication check failed: {e}", "error")
        return False

def create_service_manager():
    """Create service manager script"""
    log_step("Service Manager", "Creating service manager...")
    
    service_content = '''#!/usr/bin/env python3
"""
Service Manager for Football News Bot
مدير خدمة وكيل أخبار كرة القدم
"""

import subprocess
import sys
import time
import signal
import os
from pathlib import Path

class ServiceManager:
    def __init__(self):
        self.bot_process = None
        self.running = False
    
    def start_bot(self):
        """Start the Telegram control bot"""
        try:
            print("🚀 Starting Telegram Control Bot...")
            self.bot_process = subprocess.Popen([sys.executable, "telegram_control_bot.py"])
            self.running = True
            print("✅ Bot started successfully!")
            return True
        except Exception as e:
            print(f"❌ Failed to start bot: {e}")
            return False
    
    def stop_bot(self):
        """Stop the bot"""
        if self.bot_process:
            print("⏹️ Stopping bot...")
            self.bot_process.terminate()
            self.bot_process.wait()
            self.running = False
            print("✅ Bot stopped!")
    
    def restart_bot(self):
        """Restart the bot"""
        print("🔄 Restarting bot...")
        self.stop_bot()
        time.sleep(2)
        return self.start_bot()
    
    def monitor_bot(self):
        """Monitor bot and restart if needed"""
        while self.running:
            if self.bot_process and self.bot_process.poll() is not None:
                print("⚠️ Bot stopped unexpectedly, restarting...")
                self.restart_bot()
            time.sleep(30)

if __name__ == "__main__":
    manager = ServiceManager()
    
    def signal_handler(sig, frame):
        print("\\n🛑 Received stop signal...")
        manager.stop_bot()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    if manager.start_bot():
        try:
            manager.monitor_bot()
        except KeyboardInterrupt:
            manager.stop_bot()
'''
    
    try:
        with open('service_manager.py', 'w', encoding='utf-8') as f:
            f.write(service_content)
        log_step("Service Manager", "Service manager created ✅", "success")
        return True
    except Exception as e:
        log_step("Service Manager", f"Failed to create service manager: {e}", "error")
        return False

def show_launch_menu():
    """Show beautiful launch menu"""
    menu = f"""
{Colors.HEADER}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                      🎯 Launch Options                      ║
╚══════════════════════════════════════════════════════════════╝
{Colors.ENDC}

{Colors.OKGREEN}1. 🚀 Launch Telegram Control Bot (Recommended){Colors.ENDC}
{Colors.OKBLUE}2. 🔄 Launch Autonomous Mode{Colors.ENDC}
{Colors.OKCYAN}3. 🧪 Run System Test{Colors.ENDC}
{Colors.WARNING}4. 🏥 Run Health Check{Colors.ENDC}
{Colors.HEADER}5. 🔧 Service Manager{Colors.ENDC}
{Colors.OKCYAN}6. 🔐 Auto Authentication Service{Colors.ENDC}
{Colors.WARNING}7. 🔍 Advanced System Validation{Colors.ENDC}
{Colors.OKBLUE}8. 📊 System Status Dashboard{Colors.ENDC}
{Colors.FAIL}9. 🚪 Exit{Colors.ENDC}

{Colors.BOLD}Choose your option (1-9):{Colors.ENDC} """

    return input(menu).strip()

async def run_advanced_validation():
    """Run advanced system validation"""
    log_step("Validation", "Running advanced system validation...", "info")

    try:
        from system_validator import SystemValidator

        validator = SystemValidator()
        success = validator.run_full_validation()

        if success:
            log_step("Validation", "Advanced validation passed ✅", "success")
        else:
            log_step("Validation", "Advanced validation found issues ⚠️", "warning")

        return success

    except Exception as e:
        log_step("Validation", f"Advanced validation failed: {e}", "error")
        return False

async def run_auto_authentication():
    """Run automatic authentication if needed"""
    log_step("Auto-Auth", "Checking authentication status...", "info")

    try:
        from auto_auth_service import AutoAuthService

        auth_service = AutoAuthService()

        # Check existing credentials
        creds = auth_service.load_existing_credentials()
        if creds:
            # Test connection
            success, message = auth_service.test_blogger_connection()
            if success:
                log_step("Auto-Auth", "Authentication is valid ✅", "success")
                return True
            else:
                log_step("Auto-Auth", f"Connection test failed: {message}", "warning")

        # Check if we can do automatic auth
        if auth_service.check_client_secret():
            log_step("Auto-Auth", "client_secret.json found - auto-auth available", "info")

            # Ask user if they want auto-auth
            response = input(f"\n{Colors.WARNING}🔐 Authentication needed. Start automatic authentication? (y/n): {Colors.ENDC}").lower().strip()

            if response in ['y', 'yes', 'نعم']:
                log_step("Auto-Auth", "Starting interactive authentication...", "info")
                success = auth_service.auto_authenticate_interactive()

                if success:
                    log_step("Auto-Auth", "Authentication completed successfully ✅", "success")
                    return True
                else:
                    log_step("Auto-Auth", "Authentication failed ❌", "error")
                    return False
            else:
                log_step("Auto-Auth", "User declined auto-authentication", "warning")
                return False
        else:
            log_step("Auto-Auth", "client_secret.json not found - manual setup needed", "warning")
            return False

    except Exception as e:
        log_step("Auto-Auth", f"Auto-authentication error: {e}", "error")
        return False

async def main():
    """Main launcher function"""
    print_banner()

    # Step 1: Run advanced validation
    validation_ok = await run_advanced_validation()
    if not validation_ok:
        log_step("System", "System validation failed", "error")

        response = input(f"\n{Colors.WARNING}⚠️ Validation failed. Continue anyway? (y/n): {Colors.ENDC}").lower().strip()
        if response not in ['y', 'yes', 'نعم']:
            return False

    # Step 2: Create service manager
    create_service_manager()

    # Step 3: Check system health
    health_ok, health_report = await check_system_health()

    # Step 4: Handle authentication
    auth_ok = await run_auto_authentication()
    
    # Summary
    print(f"\n{Colors.HEADER}{Colors.BOLD}📊 System Status Summary:{Colors.ENDC}")
    print(f"{Colors.OKGREEN}✅ Python Version: OK{Colors.ENDC}")
    print(f"{Colors.OKGREEN}✅ Packages: OK{Colors.ENDC}")
    print(f"{Colors.OKGREEN}✅ Environment: OK{Colors.ENDC}")
    print(f"{Colors.OKGREEN}✅ Service Files: OK{Colors.ENDC}")
    
    if health_ok:
        print(f"{Colors.OKGREEN}✅ System Health: OK{Colors.ENDC}")
    else:
        print(f"{Colors.WARNING}⚠️ System Health: Issues Found{Colors.ENDC}")
    
    if auth_ok:
        print(f"{Colors.OKGREEN}✅ Blogger Auth: OK{Colors.ENDC}")
    else:
        print(f"{Colors.WARNING}⚠️ Blogger Auth: Setup Needed{Colors.ENDC}")
    
    # Launch menu
    while True:
        try:
            choice = show_launch_menu()
            
            if choice == '1':
                log_step("Launch", "Starting Telegram Control Bot...", "success")
                subprocess.run([sys.executable, "telegram_control_bot.py"])
                
            elif choice == '2':
                log_step("Launch", "Starting Autonomous Mode...", "success")
                subprocess.run([sys.executable, "main.py"])
                
            elif choice == '3':
                log_step("Test", "Running system test...", "success")
                subprocess.run([sys.executable, "test_system_health.py"])
                
            elif choice == '4':
                log_step("Health", "Running health check...", "success")
                health_ok, report = await check_system_health()
                
            elif choice == '5':
                log_step("Service", "Starting service manager...", "success")
                subprocess.run([sys.executable, "service_manager.py"])
                
            elif choice == '6':
                log_step("Exit", "Goodbye! 👋", "success")
                break
                
            else:
                log_step("Input", "Invalid choice. Please enter 1-6.", "error")
                
        except KeyboardInterrupt:
            log_step("Exit", "Interrupted by user. Goodbye! 👋", "warning")
            break
        except Exception as e:
            log_step("Error", f"Unexpected error: {e}", "error")
    
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}⏹️ Launcher interrupted by user{Colors.ENDC}")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Colors.FAIL}❌ Launcher failed: {e}{Colors.ENDC}")
        sys.exit(1)
