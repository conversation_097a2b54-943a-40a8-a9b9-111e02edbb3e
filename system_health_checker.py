#!/usr/bin/env python3
"""
System Health Checker
فاحص صحة النظام

This module checks the health of all system components before running the bot.
"""

import os
import asyncio
import requests
from datetime import datetime
from utils.logger import logger
from config import BLOG_ID, TELEGRAM_BOT_TOKEN, TELEGRAM_CONTROL_BOT_TOKEN

class SystemHealthChecker:
    """Checks system health and connectivity"""
    
    def __init__(self):
        self.health_status = {
            'blogger_auth': False,
            'blogger_connection': False,
            'telegram_bot': False,
            'telegram_control_bot': False,
            'internet_connection': False,
            'gemini_api': False,
            'overall_health': False
        }
        self.error_messages = []
    
    async def check_internet_connection(self):
        """Check internet connectivity"""
        try:
            response = requests.get('https://www.google.com', timeout=10)
            if response.status_code == 200:
                self.health_status['internet_connection'] = True
                logger.info("✅ Internet connection: OK")
                return True
            else:
                self.error_messages.append("Internet connection failed")
                logger.error("❌ Internet connection: Failed")
                return False
        except Exception as e:
            self.error_messages.append(f"Internet connection error: {str(e)}")
            logger.error(f"❌ Internet connection error: {e}")
            return False
    
    async def check_blogger_auth(self):
        """Check Blogger authentication"""
        try:
            from blogger_auth_telegram import BloggerAuthTelegram
            
            auth_manager = BloggerAuthTelegram()
            credentials = await auth_manager.load_credentials_from_env()
            
            if credentials and credentials.valid:
                self.health_status['blogger_auth'] = True
                logger.info("✅ Blogger authentication: OK")
                return True
            else:
                self.error_messages.append("Blogger authentication failed - no valid credentials")
                logger.error("❌ Blogger authentication: Failed")
                return False
                
        except Exception as e:
            self.error_messages.append(f"Blogger auth error: {str(e)}")
            logger.error(f"❌ Blogger auth error: {e}")
            return False
    
    async def check_blogger_connection(self):
        """Check Blogger API connection"""
        try:
            if not BLOG_ID:
                self.error_messages.append("BLOG_ID not configured")
                logger.error("❌ BLOG_ID not configured")
                return False
            
            from blogger_auth_telegram import BloggerAuthTelegram
            
            auth_manager = BloggerAuthTelegram()
            success, message = await auth_manager.test_blogger_connection(BLOG_ID)
            
            if success:
                self.health_status['blogger_connection'] = True
                logger.info("✅ Blogger connection: OK")
                return True
            else:
                self.error_messages.append(f"Blogger connection failed: {message}")
                logger.error(f"❌ Blogger connection failed: {message}")
                return False
                
        except Exception as e:
            self.error_messages.append(f"Blogger connection error: {str(e)}")
            logger.error(f"❌ Blogger connection error: {e}")
            return False
    
    async def check_telegram_bot(self):
        """Check main Telegram bot"""
        try:
            if not TELEGRAM_BOT_TOKEN:
                self.error_messages.append("TELEGRAM_BOT_TOKEN not configured")
                logger.error("❌ TELEGRAM_BOT_TOKEN not configured")
                return False
            
            # Test bot token
            url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getMe"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('ok'):
                    self.health_status['telegram_bot'] = True
                    logger.info("✅ Telegram bot: OK")
                    return True
                else:
                    self.error_messages.append("Telegram bot token invalid")
                    logger.error("❌ Telegram bot token invalid")
                    return False
            else:
                self.error_messages.append("Telegram bot API unreachable")
                logger.error("❌ Telegram bot API unreachable")
                return False
                
        except Exception as e:
            self.error_messages.append(f"Telegram bot error: {str(e)}")
            logger.error(f"❌ Telegram bot error: {e}")
            return False
    
    async def check_telegram_control_bot(self):
        """Check control Telegram bot"""
        try:
            if not TELEGRAM_CONTROL_BOT_TOKEN:
                self.error_messages.append("TELEGRAM_CONTROL_BOT_TOKEN not configured")
                logger.error("❌ TELEGRAM_CONTROL_BOT_TOKEN not configured")
                return False
            
            # Test bot token
            url = f"https://api.telegram.org/bot{TELEGRAM_CONTROL_BOT_TOKEN}/getMe"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('ok'):
                    self.health_status['telegram_control_bot'] = True
                    logger.info("✅ Telegram control bot: OK")
                    return True
                else:
                    self.error_messages.append("Telegram control bot token invalid")
                    logger.error("❌ Telegram control bot token invalid")
                    return False
            else:
                self.error_messages.append("Telegram control bot API unreachable")
                logger.error("❌ Telegram control bot API unreachable")
                return False
                
        except Exception as e:
            self.error_messages.append(f"Telegram control bot error: {str(e)}")
            logger.error(f"❌ Telegram control bot error: {e}")
            return False
    
    async def check_gemini_api(self):
        """Check Gemini API"""
        try:
            from config import GEMINI_API_KEYS
            
            if not GEMINI_API_KEYS or not GEMINI_API_KEYS[0]:
                self.error_messages.append("No Gemini API keys configured")
                logger.error("❌ No Gemini API keys configured")
                return False
            
            # Test first API key
            import google.generativeai as genai
            genai.configure(api_key=GEMINI_API_KEYS[0])
            
            model = genai.GenerativeModel('gemini-pro')
            response = model.generate_content("Test")
            
            if response and response.text:
                self.health_status['gemini_api'] = True
                logger.info("✅ Gemini API: OK")
                return True
            else:
                self.error_messages.append("Gemini API test failed")
                logger.error("❌ Gemini API test failed")
                return False
                
        except Exception as e:
            self.error_messages.append(f"Gemini API error: {str(e)}")
            logger.error(f"❌ Gemini API error: {e}")
            return False
    
    async def send_health_notification(self, force_send=False):
        """Send health status notification to Telegram"""
        try:
            if not self.health_status['telegram_control_bot'] and not force_send:
                return False
            
            # Create status message
            status_emoji = "✅" if self.health_status['overall_health'] else "❌"
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            message = f"""
🏥 **تقرير صحة النظام** {status_emoji}

⏰ **الوقت:** {timestamp}

📊 **حالة المكونات:**
{'✅' if self.health_status['internet_connection'] else '❌'} اتصال الإنترنت
{'✅' if self.health_status['blogger_auth'] else '❌'} مصادقة Blogger
{'✅' if self.health_status['blogger_connection'] else '❌'} اتصال Blogger
{'✅' if self.health_status['telegram_bot'] else '❌'} بوت التيليجرام الرئيسي
{'✅' if self.health_status['telegram_control_bot'] else '❌'} بوت التحكم
{'✅' if self.health_status['gemini_api'] else '❌'} Gemini API

🔍 **الحالة العامة:** {'سليم' if self.health_status['overall_health'] else 'يحتاج إصلاح'}
"""
            
            if self.error_messages:
                message += f"\n⚠️ **الأخطاء:**\n"
                for error in self.error_messages[:5]:  # Limit to 5 errors
                    message += f"• {error}\n"
            
            # Send via control bot
            if TELEGRAM_CONTROL_BOT_TOKEN:
                url = f"https://api.telegram.org/bot{TELEGRAM_CONTROL_BOT_TOKEN}/sendMessage"
                
                # Try to get chat ID from bot state or use a default
                chat_id = self.get_admin_chat_id()
                if chat_id:
                    data = {
                        'chat_id': chat_id,
                        'text': message,
                        'parse_mode': 'Markdown'
                    }
                    
                    response = requests.post(url, json=data, timeout=10)
                    if response.status_code == 200:
                        logger.info("✅ Health notification sent")
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to send health notification: {e}")
            return False
    
    def get_admin_chat_id(self):
        """Get admin chat ID from bot state or config"""
        try:
            # Try to read from bot state file
            if os.path.exists('bot_state.json'):
                import json
                with open('bot_state.json', 'r', encoding='utf-8') as f:
                    state = json.load(f)
                    return state.get('admin_chat_id')
            return None
        except:
            return None
    
    async def run_full_health_check(self):
        """Run complete health check"""
        logger.info("🏥 Starting system health check...")
        
        # Reset status
        self.health_status = {key: False for key in self.health_status}
        self.error_messages = []
        
        # Run all checks
        checks = [
            self.check_internet_connection(),
            self.check_telegram_control_bot(),
            self.check_telegram_bot(),
            self.check_blogger_auth(),
            self.check_blogger_connection(),
            self.check_gemini_api()
        ]
        
        results = await asyncio.gather(*checks, return_exceptions=True)
        
        # Calculate overall health
        critical_components = ['internet_connection', 'blogger_auth', 'blogger_connection', 'telegram_control_bot']
        critical_health = all(self.health_status[comp] for comp in critical_components)
        
        self.health_status['overall_health'] = critical_health
        
        # Log summary
        if critical_health:
            logger.info("✅ System health check: PASSED")
        else:
            logger.error("❌ System health check: FAILED")
            for error in self.error_messages:
                logger.error(f"   • {error}")
        
        return critical_health
    
    def get_health_report(self):
        """Get detailed health report"""
        return {
            'status': self.health_status,
            'errors': self.error_messages,
            'timestamp': datetime.now().isoformat()
        }

# Global health checker instance
health_checker = SystemHealthChecker()

async def check_system_health():
    """Quick function to check system health"""
    return await health_checker.run_full_health_check()

if __name__ == '__main__':
    # Test health check
    async def test():
        result = await health_checker.run_full_health_check()
        print(f"Health check result: {result}")
        
        report = health_checker.get_health_report()
        print(f"Health report: {report}")
    
    asyncio.run(test())
