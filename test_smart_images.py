#!/usr/bin/env python3
"""
Test script for the smart image generation system.
This script tests the new AI-powered image search, analysis, and processing.
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.smart_image_manager import smart_image_manager
from utils.image_search_apis import image_search_manager
from utils.image_analyzer import image_analyzer
from utils.image_processor import image_processor
from utils.font_manager import font_manager
from utils.logger import logger

def test_font_manager():
    """Test font management system."""
    print("🔤 Testing Font Manager...")
    
    try:
        # Test Arabic text detection
        arabic_text = "ميسي يسجل هدفاً رائعاً"
        english_text = "Me<PERSON> scores amazing goal"
        
        print(f"Arabic text detected: {font_manager.is_arabic_text(arabic_text)}")
        print(f"English text detected: {font_manager.is_arabic_text(english_text)}")
        
        # Test font path retrieval
        arabic_font = font_manager.get_font_path(arabic_text, bold=True)
        english_font = font_manager.get_font_path(english_text, bold=True)
        
        print(f"Arabic font path: {arabic_font}")
        print(f"English font path: {english_font}")
        
        # Test font availability
        font_status = font_manager.check_fonts_availability()
        print(f"Font availability: {font_status}")
        
        # Test font size suggestion
        font_size = font_manager.suggest_font_size(arabic_text, 1280, 720)
        print(f"Suggested font size: {font_size}")
        
        print("✅ Font Manager test completed")
        return True
        
    except Exception as e:
        print(f"❌ Font Manager test failed: {e}")
        return False

def test_image_search():
    """Test image search functionality."""
    print("\n🔍 Testing Image Search...")
    
    try:
        # Test entity-based search queries
        entities = {
            'players': ['ميسي', 'رونالدو'],
            'teams': ['ريال مدريد', 'برشلونة'],
            'competitions': ['دوري أبطال أوروبا']
        }
        
        queries = image_search_manager.generate_search_queries(entities)
        print(f"Generated search queries: {queries}")
        
        # Test Pixabay search (if API key available)
        if os.getenv('PIXABAY_API_KEY'):
            print("Testing Pixabay search...")
            pixabay_results = image_search_manager.search_pixabay("messi football", per_page=5)
            print(f"Pixabay results: {len(pixabay_results)} images found")
            
            if pixabay_results:
                print(f"Sample result: {pixabay_results[0].get('tags', 'No tags')}")
        else:
            print("⚠️ Pixabay API key not found - skipping Pixabay test")
        
        # Test Unsplash search (if API key available)
        if os.getenv('UNSPLASH_ACCESS_KEY'):
            print("Testing Unsplash search...")
            unsplash_results = image_search_manager.search_unsplash("football soccer", per_page=5)
            print(f"Unsplash results: {len(unsplash_results)} images found")
        else:
            print("⚠️ Unsplash API key not found - skipping Unsplash test")
        
        print("✅ Image Search test completed")
        return True
        
    except Exception as e:
        print(f"❌ Image Search test failed: {e}")
        return False

def test_image_processor():
    """Test image processing functionality."""
    print("\n🖼️ Testing Image Processor...")
    
    try:
        # Test fallback image creation
        test_title = "ميسي يسجل هدفاً تاريخياً في دوري أبطال أوروبا"
        
        print("Creating fallback image...")
        fallback_path = image_processor.create_fallback_image(test_title, "imgs/test_fallback.jpg")
        
        if fallback_path and os.path.exists(fallback_path):
            print(f"✅ Fallback image created: {fallback_path}")
        else:
            print("❌ Failed to create fallback image")
            return False
        
        # Test text wrapping
        font_path = font_manager.get_font_path(test_title, bold=True)
        if font_path:
            from PIL import ImageFont
            try:
                font = ImageFont.truetype(font_path, 48)
                wrapped_lines = image_processor.wrap_text(test_title, font, 800)
                print(f"Text wrapped into {len(wrapped_lines)} lines: {wrapped_lines}")
            except Exception as e:
                print(f"⚠️ Font loading failed: {e}")
        
        print("✅ Image Processor test completed")
        return True
        
    except Exception as e:
        print(f"❌ Image Processor test failed: {e}")
        return False

def test_smart_image_generation():
    """Test complete smart image generation."""
    print("\n🧠 Testing Smart Image Generation...")
    
    try:
        # Test article data
        article_title = "ميسي يقود باريس سان جيرمان للفوز على ريال مدريد"
        article_content = """
        في مباراة مثيرة في دوري أبطال أوروبا، تمكن ليونيل ميسي من قيادة باريس سان جيرمان 
        لتحقيق فوز مهم على ريال مدريد بنتيجة 2-1. سجل ميسي هدفين رائعين في الشوط الثاني
        ليضمن تأهل فريقه لنصف النهائي. كان أداء النجم الأرجنتيني استثنائياً طوال المباراة.
        """
        
        print(f"Testing with article: '{article_title}'")
        
        # Set lower thresholds for testing
        smart_image_manager.set_quality_thresholds(
            min_relevance_score=3.0,  # Lower threshold for testing
            max_search_results=10,
            max_analyze_images=3
        )
        
        # Generate smart image
        print("Generating smart image...")
        image_path = smart_image_manager.generate_smart_image(
            article_title, 
            article_content, 
            "imgs/test_smart_image.jpg"
        )
        
        if image_path and os.path.exists(image_path):
            print(f"✅ Smart image generated: {image_path}")
            
            # Get file size
            file_size = os.path.getsize(image_path) / 1024  # KB
            print(f"Image file size: {file_size:.1f} KB")
            
            return True
        else:
            print("❌ Smart image generation failed")
            return False
        
    except Exception as e:
        print(f"❌ Smart Image Generation test failed: {e}")
        return False

def test_api_keys():
    """Test API key availability."""
    print("\n🔑 Testing API Keys...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()

    api_keys = {
        'PIXABAY_API_KEY': os.getenv('PIXABAY_API_KEY'),
        'UNSPLASH_ACCESS_KEY': os.getenv('UNSPLASH_ACCESS_KEY'),
        'GEMINI_API_KEY_1': os.getenv('GEMINI_API_KEY_1'),
        'BLOG_ID': os.getenv('BLOG_ID'),
        'TELEGRAM_BOT_TOKEN': os.getenv('TELEGRAM_BOT_TOKEN')
    }
    
    for key_name, key_value in api_keys.items():
        if key_value:
            print(f"✅ {key_name}: Available")
        else:
            print(f"⚠️ {key_name}: Not found")
    
    if not any(api_keys.values()):
        print("❌ No API keys found! Please set up API keys for full functionality.")
        return False
    
    return True

def test_directory_structure():
    """Test required directory structure."""
    print("\n📁 Testing Directory Structure...")
    
    required_dirs = [
        'imgs',
        'fonts',
        'fonts/arabic',
        'fonts/english'
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}: Exists")
        else:
            print(f"⚠️ {dir_path}: Missing - creating...")
            os.makedirs(dir_path, exist_ok=True)
    
    return True

def main():
    """Run all tests."""
    print("🚀 Starting Smart Image Generation System Tests")
    print("=" * 60)
    
    tests = [
        test_directory_structure,
        test_api_keys,
        test_font_manager,
        test_image_processor,
        test_image_search,
        test_smart_image_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"🏁 Tests completed: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Smart image system is ready.")
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        print("\n📋 Setup checklist:")
        print("1. Set PIXABAY_API_KEY environment variable")
        print("2. Set UNSPLASH_ACCESS_KEY environment variable")
        print("3. Add font files to fonts/arabic/ and fonts/english/")
        print("4. Ensure internet connection for API calls")

if __name__ == '__main__':
    main()
