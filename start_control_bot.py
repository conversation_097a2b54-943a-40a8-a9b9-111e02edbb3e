#!/usr/bin/env python3
"""
Start the Telegram control bot
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        return False
    
    # Check Telegram control bot token
    control_token = os.getenv('TELEGRAM_CONTROL_BOT_TOKEN')
    if not control_token or control_token == 'YOUR_CONTROL_BOT_TOKEN_HERE':
        print("❌ TELEGRAM_CONTROL_BOT_TOKEN not set in .env file")
        print("Please add your Telegram control bot token to .env file")
        return False
    
    # Check if telegram_control_bot.py exists
    if not os.path.exists('telegram_control_bot.py'):
        print("❌ telegram_control_bot.py not found")
        return False
    
    print("✅ All requirements met")
    return True

def main():
    """Main function"""
    print("🤖 Starting Telegram Control Bot")
    print("=" * 40)
    
    if not check_requirements():
        print("\n❌ Requirements not met. Please fix the issues above.")
        return False
    
    try:
        # Import and run the control bot
        from telegram_control_bot import main as telegram_main
        print("🚀 Starting control bot...")
        telegram_main()
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please make sure all required packages are installed:")
        print("pip install python-telegram-bot python-dotenv")
        return False
        
    except Exception as e:
        print(f"❌ Error starting control bot: {e}")
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
