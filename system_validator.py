#!/usr/bin/env python3
"""
🔍 System Validator
فاحص النظام المتطور

Advanced system validation and auto-repair functionality.
"""

import os
import sys
import subprocess
import json
import importlib
import pkg_resources
from pathlib import Path
from datetime import datetime

class SystemValidator:
    """Advanced system validator with auto-repair"""
    
    def __init__(self):
        self.issues = []
        self.fixes_applied = []
        self.validation_report = {
            'timestamp': datetime.now().isoformat(),
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'platform': sys.platform,
            'issues_found': [],
            'fixes_applied': [],
            'status': 'unknown'
        }
    
    def log_issue(self, category, description, severity="medium"):
        """Log an issue"""
        issue = {
            'category': category,
            'description': description,
            'severity': severity,
            'timestamp': datetime.now().isoformat()
        }
        self.issues.append(issue)
        self.validation_report['issues_found'].append(issue)
        
        severity_icon = "🔴" if severity == "high" else "🟡" if severity == "medium" else "🟢"
        print(f"{severity_icon} [{category}] {description}")
    
    def log_fix(self, description):
        """Log a fix that was applied"""
        fix = {
            'description': description,
            'timestamp': datetime.now().isoformat()
        }
        self.fixes_applied.append(fix)
        self.validation_report['fixes_applied'].append(fix)
        print(f"🔧 FIX: {description}")
    
    def validate_python_version(self):
        """Validate Python version"""
        print("🐍 Validating Python version...")
        
        version = sys.version_info
        if version.major < 3:
            self.log_issue("Python", f"Python {version.major} is not supported. Need Python 3.8+", "high")
            return False
        elif version.major == 3 and version.minor < 8:
            self.log_issue("Python", f"Python 3.{version.minor} is too old. Need Python 3.8+", "high")
            return False
        else:
            print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
            return True
    
    def validate_required_packages(self):
        """Validate and install required packages"""
        print("📦 Validating required packages...")
        
        required_packages = {
            'python-dotenv': 'python_dotenv',
            'google-api-python-client': 'googleapiclient',
            'google-auth-httplib2': 'google_auth_httplib2',
            'google-auth-oauthlib': 'google_auth_oauthlib',
            'selenium': 'selenium',
            'webdriver-manager': 'webdriver_manager',
            'beautifulsoup4': 'bs4',
            'requests': 'requests',
            'google-generativeai': 'google.generativeai',
            'python-telegram-bot': 'telegram',
            'Markdown': 'markdown',
            'Pillow': 'PIL'
        }
        
        missing_packages = []
        
        for package_name, import_name in required_packages.items():
            try:
                importlib.import_module(import_name)
                print(f"✅ {package_name}")
            except ImportError:
                print(f"❌ {package_name}")
                missing_packages.append(package_name)
                self.log_issue("Packages", f"Missing package: {package_name}", "high")
        
        # Auto-install missing packages
        if missing_packages:
            print(f"\n🔧 Installing {len(missing_packages)} missing packages...")
            
            for package in missing_packages:
                try:
                    print(f"📥 Installing {package}...")
                    result = subprocess.run(
                        [sys.executable, "-m", "pip", "install", package],
                        capture_output=True, text=True, check=True
                    )
                    print(f"✅ {package} installed successfully")
                    self.log_fix(f"Installed package: {package}")
                except subprocess.CalledProcessError as e:
                    self.log_issue("Installation", f"Failed to install {package}: {e.stderr}", "high")
                    return False
                except Exception as e:
                    self.log_issue("Installation", f"Error installing {package}: {e}", "high")
                    return False
        
        return True
    
    def validate_project_structure(self):
        """Validate project file structure"""
        print("📁 Validating project structure...")
        
        required_files = [
            'main.py',
            'config.py',
            'requirements.txt',
            '.env'
        ]
        
        required_directories = [
            'utils',
            'scraper',
            'generator',
            'publisher',
            'database'
        ]
        
        # Check files
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path}")
                self.log_issue("Structure", f"Missing file: {file_path}", "high")
        
        # Check directories
        for dir_path in required_directories:
            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                print(f"✅ {dir_path}/")
            else:
                print(f"❌ {dir_path}/")
                self.log_issue("Structure", f"Missing directory: {dir_path}", "medium")
        
        return len([i for i in self.issues if i['category'] == 'Structure']) == 0
    
    def validate_environment_file(self):
        """Validate .env file"""
        print("⚙️ Validating environment configuration...")
        
        if not os.path.exists('.env'):
            self.log_issue("Environment", ".env file missing", "high")
            
            # Try to create from template
            if os.path.exists('env.example'):
                try:
                    with open('env.example', 'r', encoding='utf-8') as src:
                        content = src.read()
                    with open('.env', 'w', encoding='utf-8') as dst:
                        dst.write(content)
                    self.log_fix("Created .env file from env.example")
                    print("✅ .env file created from template")
                except Exception as e:
                    self.log_issue("Environment", f"Failed to create .env: {e}", "high")
                    return False
            else:
                self.log_issue("Environment", "No env.example template found", "high")
                return False
        
        # Check critical environment variables
        try:
            from dotenv import load_dotenv
            load_dotenv()
            
            critical_vars = [
                'TELEGRAM_CONTROL_BOT_TOKEN',
                'BLOG_ID',
                'GEMINI_API_KEY_1'
            ]
            
            for var in critical_vars:
                value = os.getenv(var)
                if not value or value.startswith('YOUR_') or value == '':
                    print(f"⚠️ {var} not configured")
                    self.log_issue("Configuration", f"{var} not properly configured", "medium")
                else:
                    print(f"✅ {var} configured")
            
        except Exception as e:
            self.log_issue("Environment", f"Error reading .env: {e}", "high")
            return False
        
        return True
    
    def validate_service_files(self):
        """Validate service files"""
        print("🔧 Validating service files...")
        
        service_files = [
            'telegram_control_bot.py',
            'system_health_checker.py',
            'blogger_auth_telegram.py',
            'auto_auth_service.py'
        ]
        
        for file_path in service_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path}")
                
                # Check if file is valid Python
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    compile(content, file_path, 'exec')
                    print(f"  ✅ Syntax valid")
                except SyntaxError as e:
                    print(f"  ❌ Syntax error: {e}")
                    self.log_issue("Service", f"Syntax error in {file_path}: {e}", "high")
                except Exception as e:
                    print(f"  ⚠️ Could not validate: {e}")
            else:
                print(f"❌ {file_path}")
                self.log_issue("Service", f"Missing service file: {file_path}", "high")
        
        return len([i for i in self.issues if i['category'] == 'Service' and i['severity'] == 'high']) == 0
    
    def create_missing_directories(self):
        """Create missing directories"""
        print("📁 Creating missing directories...")
        
        directories = [
            'logs',
            'backup_articles',
            'temp',
            'cache'
        ]
        
        for dir_path in directories:
            if not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    print(f"✅ Created {dir_path}/")
                    self.log_fix(f"Created directory: {dir_path}")
                except Exception as e:
                    self.log_issue("Directory", f"Failed to create {dir_path}: {e}", "medium")
            else:
                print(f"✅ {dir_path}/ exists")
    
    def run_full_validation(self):
        """Run complete system validation"""
        print("🔍 Starting comprehensive system validation...")
        print("=" * 60)
        
        # Reset state
        self.issues = []
        self.fixes_applied = []
        
        # Run validations
        validations = [
            ("Python Version", self.validate_python_version),
            ("Required Packages", self.validate_required_packages),
            ("Project Structure", self.validate_project_structure),
            ("Environment File", self.validate_environment_file),
            ("Service Files", self.validate_service_files),
            ("Directory Creation", self.create_missing_directories)
        ]
        
        results = []
        for name, validation_func in validations:
            print(f"\n📋 {name}")
            print("-" * 40)
            try:
                result = validation_func()
                results.append(result)
                print(f"{'✅' if result else '❌'} {name} {'passed' if result else 'failed'}")
            except Exception as e:
                print(f"❌ {name} failed with error: {e}")
                self.log_issue("Validation", f"{name} validation error: {e}", "high")
                results.append(False)
        
        # Generate summary
        print("\n" + "=" * 60)
        print("📊 VALIDATION SUMMARY")
        print("=" * 60)
        
        total_validations = len(results)
        passed_validations = sum(results)
        
        print(f"✅ Passed: {passed_validations}/{total_validations}")
        print(f"❌ Failed: {total_validations - passed_validations}/{total_validations}")
        print(f"🔧 Fixes Applied: {len(self.fixes_applied)}")
        print(f"⚠️ Issues Found: {len(self.issues)}")
        
        # Set overall status
        if passed_validations == total_validations:
            self.validation_report['status'] = 'passed'
            print("\n🎉 System validation PASSED! Ready to launch.")
        elif passed_validations >= total_validations * 0.8:
            self.validation_report['status'] = 'warning'
            print("\n⚠️ System validation passed with WARNINGS.")
        else:
            self.validation_report['status'] = 'failed'
            print("\n❌ System validation FAILED. Please fix issues before launching.")
        
        # Show critical issues
        critical_issues = [i for i in self.issues if i['severity'] == 'high']
        if critical_issues:
            print(f"\n🔴 Critical Issues ({len(critical_issues)}):")
            for issue in critical_issues:
                print(f"  • {issue['description']}")
        
        # Save report
        self.save_validation_report()
        
        return self.validation_report['status'] == 'passed'
    
    def save_validation_report(self):
        """Save validation report to file"""
        try:
            report_file = f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.validation_report, f, indent=2, ensure_ascii=False)
            print(f"\n📄 Validation report saved: {report_file}")
        except Exception as e:
            print(f"⚠️ Could not save report: {e}")

def main():
    """Main function for standalone usage"""
    print("🔍 System Validator")
    print("Advanced system validation and auto-repair")
    print("=" * 50)
    
    validator = SystemValidator()
    success = validator.run_full_validation()
    
    return success

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Validation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Validation failed: {e}")
        sys.exit(1)
