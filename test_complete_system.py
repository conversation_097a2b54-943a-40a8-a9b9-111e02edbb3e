#!/usr/bin/env python3
"""
اختبار شامل للنظام المحدث
يختبر جميع مكونات النظام بما في ذلك النشر على Blogger والنظام البديل
"""

import sys
import os
from utils.logger import logger

def test_imports():
    """اختبار استيراد جميع المكونات"""
    print("1️⃣ اختبار استيراد المكونات...")
    
    try:
        # اختبار استيراد المكونات الأساسية
        from publisher import blogger_publisher
        from publisher.fallback_publisher import fallback_publisher
        from blogger_auth_manager import BloggerAuthManager
        import config
        
        print("✅ تم استيراد جميع المكونات بنجاح")
        return True
        
    except ImportError as e:
        print(f"❌ فشل في استيراد المكونات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع في الاستيراد: {e}")
        return False

def test_configuration():
    """اختبار التكوين"""
    print("\n2️⃣ اختبار التكوين...")
    
    try:
        import config
        
        # التحقق من المتغيرات المطلوبة
        required_vars = ['BLOG_ID', 'GOOGLE_OAUTH_TOKEN']
        missing_vars = []
        
        for var in required_vars:
            if not hasattr(config, var) or not getattr(config, var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ متغيرات مفقودة: {', '.join(missing_vars)}")
            return False
        
        print(f"✅ Blog ID: {config.BLOG_ID}")
        print("✅ Google OAuth Token موجود")
        
        # التحقق من Gemini API Keys
        if hasattr(config, 'GEMINI_API_KEYS') and config.GEMINI_API_KEYS:
            print(f"✅ Gemini API Keys: {len(config.GEMINI_API_KEYS)} مفاتيح")
        else:
            print("⚠️ Gemini API Keys غير موجودة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكوين: {e}")
        return False

def test_blogger_auth():
    """اختبار مصادقة Blogger"""
    print("\n3️⃣ اختبار مصادقة Blogger...")
    
    try:
        from blogger_auth_manager import BloggerAuthManager
        
        auth_manager = BloggerAuthManager()
        
        # اختبار تحميل credentials
        creds = auth_manager.load_credentials_from_env()
        
        if creds:
            print("✅ تم تحميل credentials بنجاح")
            
            if creds.valid:
                print("✅ Credentials صالحة")
            else:
                print("⚠️ Credentials منتهية الصلاحية")
            
            # اختبار الاتصال
            success, message = auth_manager.test_blogger_connection()
            
            if success:
                print(f"✅ اختبار الاتصال: {message}")
                return True
            else:
                print(f"❌ فشل اختبار الاتصال: {message}")
                return False
        else:
            print("❌ فشل في تحميل credentials")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار مصادقة Blogger: {e}")
        return False

def test_fallback_system():
    """اختبار النظام البديل"""
    print("\n4️⃣ اختبار النظام البديل...")
    
    try:
        from publisher.fallback_publisher import fallback_publisher
        
        # بيانات اختبار
        test_data = {
            "title": "اختبار النظام البديل",
            "content": "## هذا اختبار\n\nمحتوى تجريبي للنظام البديل",
            "keywords": ["اختبار", "نظام بديل"],
            "image_path": None
        }
        
        # اختبار النشر البديل
        result = fallback_publisher.publish_with_fallback(
            test_data["title"],
            test_data["content"],
            test_data["keywords"],
            test_data["image_path"],
            "اختبار النظام"
        )
        
        if result:
            print(f"✅ النظام البديل يعمل: {result}")
            
            # اختبار الإحصائيات
            stats = fallback_publisher.get_backup_stats()
            print(f"📊 إحصائيات النسخ الاحتياطية: {stats}")
            
            return True
        else:
            print("❌ فشل النظام البديل")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام البديل: {e}")
        return False

def test_blogger_publishing():
    """اختبار النشر على Blogger"""
    print("\n5️⃣ اختبار النشر على Blogger...")
    
    try:
        from publisher import blogger_publisher
        
        # بيانات اختبار
        test_title = "اختبار النظام المحدث"
        test_content = """
## اختبار النظام المحدث

هذا مقال تجريبي لاختبار النظام المحدث.

**الميزات الجديدة:**
- نظام fallback محسن
- معالجة أفضل للأخطاء
- تجديد Token تلقائي

### الخلاصة
النظام يعمل بشكل صحيح!
"""
        test_keywords = ["اختبار", "نظام محدث", "blogger"]
        
        # محاولة النشر
        result = blogger_publisher.publish_to_blogger(
            test_title, test_content, test_keywords, None
        )
        
        if result:
            print(f"✅ تم النشر بنجاح: {result}")
            
            # التحقق من نوع النتيجة
            if "blogspot.com" in result:
                if "fallback" in result or "test-post" in result:
                    print("⚠️ تم استخدام النظام البديل")
                else:
                    print("✅ تم النشر الفعلي على Blogger")
            
            return True
        else:
            print("❌ فشل النشر")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النشر: {e}")
        return False

def test_complete_workflow():
    """اختبار سير العمل الكامل"""
    print("\n6️⃣ اختبار سير العمل الكامل...")
    
    try:
        # محاكاة دورة كاملة
        from scraper import kooora_scraper_simple
        from generator import content_generator
        from publisher import blogger_publisher
        
        print("🔍 جلب الأخبار...")
        articles = kooora_scraper_simple.scrape_kooora()
        
        if articles:
            print(f"✅ تم جلب {len(articles)} مقال")
            
            # اختبار مقال واحد
            test_article = articles[0]
            print(f"📰 اختبار مقال: {test_article['title'][:50]}...")
            
            # توليد محتوى
            print("✍️ توليد المحتوى...")
            content = content_generator.generate_article(test_article['title'], test_article.get('source', 'Kooora'))

            if content:
                print("✅ تم توليد المحتوى")

                # توليد عنوان جذاب
                catchy_title = content_generator.generate_catchy_title(test_article['title'])
                print(f"📝 العنوان الجذاب: {catchy_title}")

                # توليد كلمات مفتاحية
                keywords = content_generator.generate_seo_keywords(catchy_title)
                print(f"🔑 الكلمات المفتاحية: {keywords}")

                # النشر
                print("📤 النشر...")
                result = blogger_publisher.publish_to_blogger(
                    catchy_title, content, keywords, None
                )

                if result:
                    print(f"✅ سير العمل الكامل نجح: {result}")
                    return True
                else:
                    print("❌ فشل النشر في سير العمل")
                    return False
            else:
                print("❌ فشل توليد المحتوى")
                return False
        else:
            print("❌ لم يتم جلب أي مقالات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار سير العمل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الاختبار الشامل للنظام المحدث")
    print("=" * 60)
    
    tests = [
        ("استيراد المكونات", test_imports),
        ("التكوين", test_configuration),
        ("مصادقة Blogger", test_blogger_auth),
        ("النظام البديل", test_fallback_system),
        ("النشر على Blogger", test_blogger_publishing),
        ("سير العمل الكامل", test_complete_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ غير متوقع - {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للعمل")
        return True
    else:
        print(f"⚠️ {total - passed} اختبار فشل. يحتاج النظام لمراجعة")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
