#!/usr/bin/env python3
"""
Blogger Publisher with Telegram Authentication
ناشر بلوجر مع مصادقة التيليجرام

This module publishes articles to Blogger using the new Telegram authentication system.
"""

import json
import asyncio
import base64
import markdown
from datetime import datetime
from config import BLOG_ID, GOOGLE_OAUTH_TOKEN
from utils.logger import logger
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from googleapiclient.discovery import build

# Import the new Telegram auth manager
try:
    from blogger_auth_telegram import BloggerAuthTelegram
    AUTH_MANAGER_AVAILABLE = True
    logger.info("✅ Telegram BloggerAuth available")
except ImportError:
    logger.warning("⚠️ Telegram BloggerAuth not available, using fallback")
    AUTH_MANAGER_AVAILABLE = False

SCOPES = ['https://www.googleapis.com/auth/blogger']

def get_credentials():
    """Get valid Google OAuth credentials for Blogger API"""
    # Try new Telegram auth manager first
    if AUTH_MANAGER_AVAILABLE:
        try:
            auth_manager = BloggerAuthTelegram()
            
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            credentials = loop.run_until_complete(auth_manager.load_credentials_from_env())
            loop.close()
            
            if credentials:
                logger.info("✅ Credentials loaded via Telegram auth manager")
                return credentials
        except Exception as e:
            logger.warning(f"⚠️ Telegram auth manager failed: {e}")
    
    # Fallback to legacy method
    try:
        if not GOOGLE_OAUTH_TOKEN:
            logger.error("GOOGLE_OAUTH_TOKEN not found in environment variables")
            return None

        # Parse the token data
        if isinstance(GOOGLE_OAUTH_TOKEN, str):
            token_data = json.loads(GOOGLE_OAUTH_TOKEN)
        else:
            token_data = GOOGLE_OAUTH_TOKEN

        # Create credentials object
        creds = Credentials.from_authorized_user_info(token_data, SCOPES)

        # Check if credentials are valid
        if creds and creds.valid:
            logger.info("✅ Valid credentials loaded (legacy)")
            return creds
        elif creds and creds.expired and creds.refresh_token:
            logger.info("🔄 Refreshing expired credentials...")
            try:
                creds.refresh(Request())
                logger.info("✅ Credentials refreshed successfully")
                return creds
            except Exception as e:
                logger.error(f"❌ Failed to refresh credentials: {e}")
                return None
        else:
            logger.error("❌ Invalid credentials")
            return None

    except json.JSONDecodeError as e:
        logger.error(f"❌ Invalid JSON in GOOGLE_OAUTH_TOKEN: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ Error loading credentials: {e}")
        return None

def get_image_base64(image_path):
    """Convert image to base64 string"""
    try:
        if not image_path:
            return None
        
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
            base64_string = base64.b64encode(image_data).decode('utf-8')
            logger.info(f"✅ Image converted to base64: {image_path}")
            return base64_string
    except Exception as e:
        logger.error(f"❌ Failed to convert image to base64: {e}")
        return None

def _publish_with_fallback(title, content, keywords, image_path):
    """Fallback publishing method that saves to HTML file"""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_title = safe_title.replace(' ', '_')[:50]
        
        filename = f"backup_articles/{timestamp}_{safe_title}.html"
        
        # Create backup directory if it doesn't exist
        import os
        os.makedirs("backup_articles", exist_ok=True)
        
        # Create HTML content
        html_content = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <meta name="keywords" content="{keywords}">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }}
        .content {{ margin: 20px 0; }}
        .footer {{ border-top: 1px solid #ccc; padding-top: 10px; margin-top: 20px; color: #666; }}
        .keywords {{ background: #f5f5f5; padding: 10px; margin: 10px 0; }}
        img {{ max-width: 100%; height: auto; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{title}</h1>
        <p><strong>تاريخ الإنشاء:</strong> {datetime.now().isoformat()}</p>
    </div>
    
    <div class="content">
        {content}
    </div>
    
    <div class="keywords">
        <strong>الكلمات المفتاحية:</strong> {keywords}
    </div>
    
    <div class="footer">
        <p><strong>سبب النشر البديل:</strong> مشكلة في مصادقة Blogger</p>
        <p>تم إنشاء هذا الملف تلقائياً بواسطة نظام النشر البديل</p>
    </div>
</body>
</html>
"""
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"✅ Article saved as fallback: {filename}")
        return f"file://{os.path.abspath(filename)}"
        
    except Exception as e:
        logger.error(f"❌ Fallback publishing failed: {e}")
        return None

def publish_to_blogger(title, content, keywords, image_path=None):
    """Publish an article to Blogger"""
    try:
        logger.info(f"📝 Publishing article: {title}")
        
        if not BLOG_ID:
            logger.error("❌ BLOG_ID not configured")
            return _publish_with_fallback(title, content, keywords, image_path)
        
        # Get credentials
        credentials = get_credentials()
        if not credentials:
            logger.error("❌ No valid credentials available")
            return _publish_with_fallback(title, content, keywords, image_path)
        
        # Build Blogger service
        service = build('blogger', 'v3', credentials=credentials)
        
        # Convert markdown to HTML if needed
        if content.startswith('#') or '**' in content or '*' in content:
            html_content = markdown.markdown(content)
        else:
            html_content = content
        
        # Add image if provided
        if image_path:
            image_base64 = get_image_base64(image_path)
            if image_base64:
                image_html = f'<img src="data:image/png;base64,{image_base64}" alt="{title}" style="max-width: 100%; height: auto;" />'
                html_content = f"{image_html}<br /><br />{html_content}"
        
        # Clean keywords for Blogger labels
        if keywords:
            clean_keywords = [k.strip() for k in keywords.split(',') if k.strip()]
            # Limit to 200 characters total
            keywords_str = ', '.join(clean_keywords)
            if len(keywords_str) > 200:
                keywords_str = keywords_str[:197] + "..."
                clean_keywords = [k.strip() for k in keywords_str.split(',') if k.strip()]
        else:
            clean_keywords = []
        
        # Create post body
        body = {
            'title': title,
            'content': html_content,
            'labels': clean_keywords
        }
        
        # Publish the post
        posts = service.posts()
        insert_req = posts.insert(blogId=BLOG_ID, body=body, isDraft=False)
        post = insert_req.execute()
        
        post_url = post.get('url', 'Unknown URL')
        logger.info(f"✅ Article published successfully: {post_url}")
        return post_url
        
    except Exception as e:
        logger.error(f"❌ Failed to publish to Blogger: {e}")
        return _publish_with_fallback(title, content, keywords, image_path)

# For backward compatibility
def publish_article(title, content, keywords, image_path=None):
    """Legacy function name for backward compatibility"""
    return publish_to_blogger(title, content, keywords, image_path)

if __name__ == '__main__':
    # Test publishing
    test_title = "اختبار النشر الجديد"
    test_content = "هذا اختبار لنظام النشر الجديد مع مصادقة التيليجرام"
    test_keywords = "اختبار, نظام جديد, تيليجرام"
    
    result = publish_to_blogger(test_title, test_content, test_keywords)
    print(f"نتيجة النشر: {result}")
