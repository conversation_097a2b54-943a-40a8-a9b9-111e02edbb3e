#!/usr/bin/env python3
"""
Test System Health Checker
اختبار فاحص صحة النظام

This script tests the system health checker functionality.
"""

import asyncio
import sys
from system_health_checker import health_checker

async def test_individual_checks():
    """Test individual health checks"""
    print("🧪 Testing individual health checks...")
    print("-" * 50)
    
    # Test internet connection
    print("1. Testing internet connection...")
    result = await health_checker.check_internet_connection()
    print(f"   Result: {'✅ PASS' if result else '❌ FAIL'}")
    
    # Test Telegram control bot
    print("2. Testing Telegram control bot...")
    result = await health_checker.check_telegram_control_bot()
    print(f"   Result: {'✅ PASS' if result else '❌ FAIL'}")
    
    # Test Telegram main bot
    print("3. Testing Telegram main bot...")
    result = await health_checker.check_telegram_bot()
    print(f"   Result: {'✅ PASS' if result else '❌ FAIL'}")
    
    # Test Blogger auth
    print("4. Testing Blogger authentication...")
    result = await health_checker.check_blogger_auth()
    print(f"   Result: {'✅ PASS' if result else '❌ FAIL'}")
    
    # Test Blogger connection
    print("5. Testing Blogger connection...")
    result = await health_checker.check_blogger_connection()
    print(f"   Result: {'✅ PASS' if result else '❌ FAIL'}")
    
    # Test Gemini API
    print("6. Testing Gemini API...")
    result = await health_checker.check_gemini_api()
    print(f"   Result: {'✅ PASS' if result else '❌ FAIL'}")

async def test_full_health_check():
    """Test full health check"""
    print("\n🏥 Testing full health check...")
    print("-" * 50)
    
    result = await health_checker.run_full_health_check()
    
    print(f"Overall health: {'✅ HEALTHY' if result else '❌ UNHEALTHY'}")
    
    # Get detailed report
    report = health_checker.get_health_report()
    
    print("\nDetailed status:")
    for component, status in report['status'].items():
        if component != 'overall_health':
            print(f"  {component}: {'✅' if status else '❌'}")
    
    if report['errors']:
        print("\nErrors found:")
        for error in report['errors']:
            print(f"  • {error}")
    
    return result

async def test_health_notification():
    """Test health notification"""
    print("\n📱 Testing health notification...")
    print("-" * 50)
    
    try:
        result = await health_checker.send_health_notification(force_send=True)
        print(f"Notification sent: {'✅ SUCCESS' if result else '❌ FAILED'}")
    except Exception as e:
        print(f"Notification error: ❌ {e}")

def print_configuration_status():
    """Print current configuration status"""
    print("⚙️ Configuration Status:")
    print("-" * 50)
    
    from config import (
        TELEGRAM_BOT_TOKEN, TELEGRAM_CONTROL_BOT_TOKEN, 
        BLOG_ID, GEMINI_API_KEYS
    )
    
    print(f"TELEGRAM_BOT_TOKEN: {'✅ Set' if TELEGRAM_BOT_TOKEN else '❌ Missing'}")
    print(f"TELEGRAM_CONTROL_BOT_TOKEN: {'✅ Set' if TELEGRAM_CONTROL_BOT_TOKEN else '❌ Missing'}")
    print(f"BLOG_ID: {'✅ Set' if BLOG_ID else '❌ Missing'}")
    print(f"GEMINI_API_KEYS: {'✅ Set' if GEMINI_API_KEYS and GEMINI_API_KEYS[0] else '❌ Missing'}")
    
    # Check .env file
    import os
    print(f".env file: {'✅ Exists' if os.path.exists('.env') else '❌ Missing'}")

async def main():
    """Main test function"""
    print("🚀 System Health Checker Test")
    print("=" * 60)
    
    # Print configuration status
    print_configuration_status()
    print()
    
    # Test individual checks
    await test_individual_checks()
    
    # Test full health check
    overall_health = await test_full_health_check()
    
    # Test notification (optional)
    test_notification = input("\nDo you want to test health notification? (y/n): ").lower().strip()
    if test_notification in ['y', 'yes', 'نعم']:
        await test_health_notification()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    if overall_health:
        print("✅ System is healthy and ready to run!")
        print("🚀 You can start the bot with confidence.")
    else:
        print("❌ System has health issues that need attention.")
        print("🔧 Please fix the issues before running the bot.")
        
        # Show specific recommendations
        report = health_checker.get_health_report()
        print("\n🔧 Recommendations:")
        
        if not report['status']['internet_connection']:
            print("  • Check your internet connection")
        
        if not report['status']['telegram_control_bot']:
            print("  • Verify TELEGRAM_CONTROL_BOT_TOKEN in .env file")
            print("  • Make sure the bot token is valid")
        
        if not report['status']['telegram_bot']:
            print("  • Verify TELEGRAM_BOT_TOKEN in .env file")
            print("  • Make sure the bot token is valid")
        
        if not report['status']['blogger_auth']:
            print("  • Set up Blogger authentication via Telegram bot")
            print("  • Use the '🔐 إدارة مصادقة Blogger' option in the bot")
        
        if not report['status']['blogger_connection']:
            print("  • Check BLOG_ID in .env file")
            print("  • Verify Blogger API access")
        
        if not report['status']['gemini_api']:
            print("  • Check GEMINI_API_KEY values in .env file")
            print("  • Verify API quota and permissions")
    
    return overall_health

if __name__ == '__main__':
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")
        sys.exit(1)
