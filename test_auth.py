#!/usr/bin/env python3
"""
Test script for Blogger authentication
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from auto_auth_service import AutoAuthService
from blogger_auth_telegram import Blogger<PERSON>uthTelegram

def test_auto_auth():
    """Test auto authentication service"""
    print("🔐 Testing Auto Authentication Service")
    print("=" * 50)
    
    service = AutoAuthService()
    
    # Check existing credentials
    print("1. Checking existing credentials...")
    creds = service.load_existing_credentials()
    if creds:
        print("✅ Valid credentials found")
        
        # Test connection
        success, message = service.test_blogger_connection()
        if success:
            print(f"✅ Blogger connection test: {message}")
            return True
        else:
            print(f"❌ Blogger test failed: {message}")
    else:
        print("❌ No valid credentials found")
    
    # Start interactive authentication
    print("\n2. Starting interactive authentication...")
    success = service.auto_authenticate_interactive()
    
    if success:
        print("🎉 Authentication completed successfully!")
        return True
    else:
        print("❌ Authentication failed")
        return False

async def test_telegram_auth():
    """Test Telegram authentication manager"""
    print("\n🤖 Testing Telegram Authentication Manager")
    print("=" * 50)
    
    auth_manager = BloggerAuthTelegram()
    
    # Check existing credentials
    print("1. Checking existing credentials...")
    credentials = await auth_manager.load_credentials_from_env()
    
    if credentials and credentials.valid:
        print("✅ Valid credentials found")
        
        # Test Blogger connection
        blog_id = os.getenv('BLOG_ID')
        if blog_id:
            success, message = await auth_manager.test_blogger_connection(blog_id)
            if success:
                print(f"✅ Blogger connection test: {message}")
                return True
            else:
                print(f"❌ Blogger test failed: {message}")
        else:
            print("⚠️ No BLOG_ID found in environment")
    else:
        print("❌ No valid credentials found")
    
    return False

def main():
    """Main test function"""
    print("🧪 Blogger Authentication Test Suite")
    print("=" * 60)
    
    # Test 1: Auto Auth Service
    auto_auth_success = test_auto_auth()
    
    # Test 2: Telegram Auth Manager
    telegram_auth_success = asyncio.run(test_telegram_auth())
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"Auto Auth Service: {'✅ PASS' if auto_auth_success else '❌ FAIL'}")
    print(f"Telegram Auth Manager: {'✅ PASS' if telegram_auth_success else '❌ FAIL'}")
    
    if auto_auth_success or telegram_auth_success:
        print("\n🎉 At least one authentication method is working!")
        return True
    else:
        print("\n❌ All authentication methods failed")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
