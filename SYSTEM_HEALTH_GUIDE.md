# 🏥 دليل صحة النظام - وكيل أخبار كرة القدم

## 📋 نظرة عامة

تم إضافة نظام فحص صحة شامل للوكيل يتحقق من جميع المكونات الأساسية قبل التشغيل ويمنع العمل في حالة وجود مشاكل.

## 🔍 المكونات التي يتم فحصها

### 1. اتصال الإنترنت 🌐
- **الوظيفة:** التحقق من الاتصال بالإنترنت
- **الأهمية:** حرجة - بدونها لا يمكن الوصول لأي خدمة
- **الفحص:** محاولة الوصول لـ Google

### 2. مصادقة Blogger 🔐
- **الوظيفة:** التحقق من صحة بيانات مصادقة Google OAuth
- **الأهمية:** حرجة - بدونها لا يمكن النشر
- **الفحص:** تحميل وفحص صحة الـ credentials

### 3. اتصال Blogger 📝
- **الوظيفة:** التحقق من إمكانية الاتصال بـ Blogger API
- **الأهمية:** حرجة - بدونها لا يمكن النشر
- **الفحص:** محاولة الوصول لمعلومات المدونة

### 4. بوت التيليجرام الرئيسي 📱
- **الوظيفة:** التحقق من صحة توكن البوت الرئيسي
- **الأهمية:** متوسطة - للإشعارات
- **الفحص:** استدعاء getMe API

### 5. بوت التحكم 🤖
- **الوظيفة:** التحقق من صحة توكن بوت التحكم
- **الأهمية:** حرجة - للتحكم في النظام
- **الفحص:** استدعاء getMe API

### 6. Gemini API 🧠
- **الوظيفة:** التحقق من صحة مفاتيح Gemini
- **الأهمية:** حرجة - لتوليد المحتوى
- **الفحص:** إرسال طلب اختبار

## 🚨 سلوك النظام عند فشل الفحص

### المكونات الحرجة
إذا فشل أي من هذه المكونات، **سيتوقف الوكيل عن العمل**:
- اتصال الإنترنت
- مصادقة Blogger
- اتصال Blogger
- بوت التحكم

### المكونات غير الحرجة
إذا فشلت هذه المكونات، **سيستمر الوكيل بالعمل مع تحذيرات**:
- بوت التيليجرام الرئيسي (ستفقد الإشعارات)
- Gemini API (سيحاول استخدام مفاتيح بديلة)

## 🔧 كيفية استخدام فاحص صحة النظام

### 1. من خلال بوت التيليجرام
```
/start → 🏥 فحص صحة النظام
```

### 2. من خلال سكريبت التشغيل
```bash
python run_telegram_bot.py
# اختر الخيار 5: 🏥 Test System Health
```

### 3. مباشرة
```bash
python test_system_health.py
```

## 📊 تفسير نتائج الفحص

### ✅ النظام سليم
```
🏥 تقرير صحة النظام ✅
📊 حالة المكونات:
✅ اتصال الإنترنت
✅ مصادقة Blogger
✅ اتصال Blogger
✅ بوت التيليجرام الرئيسي
✅ بوت التحكم
✅ Gemini API
🔍 الحالة العامة: سليم ✅
```

### ❌ النظام يحتاج إصلاح
```
🏥 تقرير صحة النظام ❌
📊 حالة المكونات:
✅ اتصال الإنترنت
❌ مصادقة Blogger
❌ اتصال Blogger
✅ بوت التيليجرام الرئيسي
✅ بوت التحكم
✅ Gemini API
🔍 الحالة العامة: يحتاج إصلاح ❌

⚠️ الأخطاء المكتشفة:
• Blogger authentication failed - no valid credentials
• Blogger connection failed: Invalid credentials
```

## 🛠️ حل المشاكل الشائعة

### ❌ مشكلة اتصال الإنترنت
**الأعراض:** `❌ اتصال الإنترنت`
**الحلول:**
1. تحقق من اتصال الإنترنت
2. تحقق من إعدادات الـ Proxy إن وجدت
3. تحقق من جدار الحماية

### ❌ مشكلة مصادقة Blogger
**الأعراض:** `❌ مصادقة Blogger`
**الحلول:**
1. استخدم بوت التيليجرام: `🔐 إدارة مصادقة Blogger`
2. احذف بيانات المصادقة القديمة
3. أعد المصادقة بحساب صحيح

### ❌ مشكلة اتصال Blogger
**الأعراض:** `❌ اتصال Blogger`
**الحلول:**
1. تحقق من صحة `BLOG_ID` في ملف `.env`
2. تأكد من أن الحساب له صلاحية على المدونة
3. تحقق من تفعيل Blogger API في Google Cloud Console

### ❌ مشكلة بوت التيليجرام
**الأعراض:** `❌ بوت التيليجرام الرئيسي` أو `❌ بوت التحكم`
**الحلول:**
1. تحقق من صحة التوكن في ملف `.env`
2. تأكد من أن البوت مفعل مع @BotFather
3. تحقق من عدم وجود مسافات إضافية في التوكن

### ❌ مشكلة Gemini API
**الأعراض:** `❌ Gemini API`
**الحلول:**
1. تحقق من صحة مفاتيح API في ملف `.env`
2. تحقق من الكوتا المتاحة
3. تأكد من تفعيل Gemini API في Google Cloud Console

## 📱 الإشعارات التلقائية

### متى يتم الإرسال
- عند فشل فحص صحة النظام
- عند بداية كل دورة (إذا كان هناك مشاكل)
- عند طلب فحص يدوي

### محتوى الإشعار
- حالة جميع المكونات
- الأخطاء المكتشفة
- وقت الفحص
- توصيات للإصلاح

## 🔄 الفحص التلقائي

### متى يحدث
- قبل بداية كل دورة من دورات الوكيل
- قبل محاولة النشر على Blogger
- عند طلب فحص يدوي

### ماذا يحدث عند الفشل
1. **توقف الدورة الحالية**
2. **إرسال إشعار بالمشكلة**
3. **تسجيل الأخطاء في ملف السجل**
4. **انتظار الدورة التالية (30 دقيقة)**

## 🎯 أفضل الممارسات

### 1. فحص دوري
- شغل فحص صحة النظام يومياً
- راقب الإشعارات في التيليجرام
- تحقق من ملفات السجل

### 2. الصيانة الوقائية
- جدد مفاتيح API قبل انتهاء صلاحيتها
- احتفظ بنسخة احتياطية من إعدادات المصادقة
- راقب كوتا الـ APIs

### 3. الاستجابة السريعة
- أصلح المشاكل فور اكتشافها
- لا تتجاهل تحذيرات النظام
- استخدم وضع الاختبار للتحقق من الإصلاحات

## 📞 الدعم

### ملفات السجل
```
logs/bot.log - سجل النظام الرئيسي
```

### أوامر التشخيص
```bash
# فحص شامل
python test_system_health.py

# فحص سريع
python -c "import asyncio; from system_health_checker import check_system_health; print(asyncio.run(check_system_health()))"
```

### معلومات مفيدة للدعم
- نتائج فحص صحة النظام
- محتوى ملف `.env` (بدون كشف المفاتيح)
- رسائل الخطأ من ملف السجل
- إصدار Python والمكتبات

---

**🎉 مع نظام فحص صحة النظام، ستتمكن من تشغيل وكيل الأخبار بثقة وأمان!**
