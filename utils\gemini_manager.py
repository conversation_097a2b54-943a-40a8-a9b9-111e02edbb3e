
import google.generativeai as genai
from config import GEMINI_API_KEYS
from utils.logger import logger
import random

class GeminiManager:
    """Manages the pool of Gemini API keys and handles key rotation."""
    def __init__(self):
        if not GEMINI_API_KEYS:
            raise ValueError("No Gemini API keys found in the configuration.")
        self.keys = GEMINI_API_KEYS
        self.current_key_index = random.randint(0, len(self.keys) - 1)

    def get_next_key(self):
        """Rotates to the next key in the list."""
        self.current_key_index = (self.current_key_index + 1) % len(self.keys)
        return self.keys[self.current_key_index]

    def get_model(self):
        """Configures and returns a Gemini model instance with the current API key."""
        max_retries = min(len(self.keys), 5)  # Try max 5 keys to avoid long delays
        failed_keys = []

        for attempt in range(max_retries):
            try:
                api_key = self.keys[self.current_key_index]
                genai.configure(api_key=api_key)

                # Test the key with a simple request
                # Try different model names, prioritizing the latest models
                model_names = [
                    'gemini-2.0-flash-exp',  # Latest experimental model
                    'gemini-1.5-pro-002',    # Latest stable pro model
                    'gemini-1.5-flash-002',  # Latest stable flash model
                    'gemini-1.5-pro',        # Fallback pro
                    'gemini-1.5-flash',      # Fallback flash
                    'gemini-pro'             # Legacy fallback
                ]
                model = None

                for model_name in model_names:
                    try:
                        model = genai.GenerativeModel(model_name)
                        test_response = model.generate_content("Test")
                        logger.info(f"✅ Successfully using model: {model_name}")
                        break
                    except Exception as model_error:
                        logger.warning(f"⚠️ Model {model_name} failed: {str(model_error)[:100]}")
                        continue

                if not model:
                    raise Exception("No working model found")

                logger.info(f"Successfully configured Gemini with key index: {self.current_key_index}")
                return model
            except Exception as e:
                error_msg = str(e)
                failed_keys.append(f"Key {self.current_key_index}: {error_msg[:100]}")
                logger.warning(f"Failed to configure Gemini with key index {self.current_key_index}: {error_msg[:100]}")
                self.get_next_key() # Rotate to the next key

        logger.error(f"Failed to configure Gemini after {max_retries} attempts. Failed keys: {failed_keys}")
        raise Exception(f"All tested Gemini API keys are failing. Tried {max_retries} keys.")

# Instantiate the manager to be used across the application
gemini_manager = GeminiManager()
