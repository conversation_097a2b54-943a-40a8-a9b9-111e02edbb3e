"""
نظام النشر البديل (Fallback Publisher)
يوفر بدائل متعددة للنشر عند فشل النشر الأساسي على Blogger
"""

import os
import json
import markdown
from datetime import datetime
from utils.logger import logger

class FallbackPublisher:
    """ناشر بديل يوفر عدة خيارات للنشر"""
    
    def __init__(self):
        self.fallback_methods = [
            self._save_to_local_file,
            self._save_to_json_archive,
            self._create_mock_url
        ]
    
    def publish_with_fallback(self, title, content, keywords, image_path, error_reason="Unknown"):
        """نشر باستخدام النظام البديل"""
        
        logger.info(f"🔄 Starting fallback publishing for: '{title}'")
        logger.info(f"📝 Fallback reason: {error_reason}")
        
        # تحويل المحتوى إلى HTML إذا لزم الأمر
        html_content = self._convert_to_html(content)
        
        # معلومات المقال
        article_data = {
            "title": title,
            "content": html_content,
            "keywords": keywords,
            "image_path": image_path,
            "created_at": datetime.now().isoformat(),
            "fallback_reason": error_reason
        }
        
        # تجربة طرق النشر البديلة
        for i, method in enumerate(self.fallback_methods, 1):
            try:
                logger.info(f"🔄 Trying fallback method {i}/{len(self.fallback_methods)}")
                result = method(article_data)
                
                if result:
                    logger.info(f"✅ Fallback method {i} succeeded: {result}")
                    return result
                    
            except Exception as e:
                logger.error(f"❌ Fallback method {i} failed: {e}")
                continue
        
        # إذا فشلت جميع الطرق
        logger.error("❌ All fallback methods failed")
        return self._create_emergency_url(title)
    
    def _convert_to_html(self, content):
        """تحويل المحتوى إلى HTML"""
        try:
            # التحقق من وجود رموز Markdown
            if '##' in content or '**' in content or '*' in content:
                html_content = markdown.markdown(
                    content, 
                    extensions=['fenced_code', 'tables', 'nl2br']
                )
                logger.info("✅ Converted Markdown to HTML")
                return html_content
            else:
                # إضافة تنسيق HTML أساسي
                html_content = content.replace('\n', '<br>')
                return f"<div>{html_content}</div>"
                
        except Exception as e:
            logger.error(f"❌ Failed to convert to HTML: {e}")
            return content
    
    def _save_to_local_file(self, article_data):
        """حفظ المقال في ملف محلي"""
        try:
            # إنشاء مجلد للمقالات المحفوظة
            backup_dir = "backup_articles"
            os.makedirs(backup_dir, exist_ok=True)
            
            # إنشاء اسم ملف آمن
            safe_title = "".join(c for c in article_data["title"] if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_title = safe_title.replace(' ', '_')[:50]  # تحديد الطول
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{safe_title}.html"
            filepath = os.path.join(backup_dir, filename)
            
            # إنشاء محتوى HTML كامل
            html_template = f"""
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{article_data['title']}</title>
    <meta name="keywords" content="{', '.join(article_data['keywords'])}">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }}
        .content {{ margin: 20px 0; }}
        .footer {{ border-top: 1px solid #ccc; padding-top: 10px; margin-top: 20px; color: #666; }}
        .keywords {{ background: #f5f5f5; padding: 10px; margin: 10px 0; }}
        img {{ max-width: 100%; height: auto; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{article_data['title']}</h1>
        <p><strong>تاريخ الإنشاء:</strong> {article_data['created_at']}</p>
    </div>
    
    <div class="content">
        {article_data['content']}
    </div>
    
    <div class="keywords">
        <strong>الكلمات المفتاحية:</strong> {', '.join(article_data['keywords'])}
    </div>
    
    <div class="footer">
        <p><strong>سبب النشر البديل:</strong> {article_data['fallback_reason']}</p>
        <p>تم إنشاء هذا الملف تلقائياً بواسطة نظام النشر البديل</p>
    </div>
</body>
</html>
"""
            
            # حفظ الملف
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_template)
            
            # إنشاء رابط محلي
            local_url = f"file:///{os.path.abspath(filepath).replace(os.sep, '/')}"
            
            logger.info(f"✅ Article saved to local file: {filepath}")
            return local_url
            
        except Exception as e:
            logger.error(f"❌ Failed to save to local file: {e}")
            return None
    
    def _save_to_json_archive(self, article_data):
        """حفظ المقال في أرشيف JSON"""
        try:
            archive_file = "backup_articles/articles_archive.json"
            
            # إنشاء المجلد إذا لم يكن موجوداً
            os.makedirs(os.path.dirname(archive_file), exist_ok=True)
            
            # قراءة الأرشيف الحالي
            articles = []
            if os.path.exists(archive_file):
                try:
                    with open(archive_file, 'r', encoding='utf-8') as f:
                        articles = json.load(f)
                except:
                    articles = []
            
            # إضافة المقال الجديد
            article_data['id'] = len(articles) + 1
            articles.append(article_data)
            
            # حفظ الأرشيف المحدث
            with open(archive_file, 'w', encoding='utf-8') as f:
                json.dump(articles, f, ensure_ascii=False, indent=2)
            
            # إنشاء رابط للأرشيف
            archive_url = f"file:///{os.path.abspath(archive_file).replace(os.sep, '/')}"
            
            logger.info(f"✅ Article saved to JSON archive: {archive_file}")
            return f"{archive_url}#article-{article_data['id']}"
            
        except Exception as e:
            logger.error(f"❌ Failed to save to JSON archive: {e}")
            return None
    
    def _create_mock_url(self, article_data):
        """إنشاء رابط وهمي للمقال"""
        try:
            # إنشاء معرف فريد للمقال
            article_id = abs(hash(article_data['title'])) % 10000
            timestamp = datetime.now().strftime("%Y%m%d")
            
            mock_url = f"https://football-news-blog.blogspot.com/{timestamp}/fallback-post-{article_id}.html"
            
            logger.info(f"✅ Created mock URL: {mock_url}")
            return mock_url
            
        except Exception as e:
            logger.error(f"❌ Failed to create mock URL: {e}")
            return None
    
    def _create_emergency_url(self, title):
        """إنشاء رابط طوارئ عند فشل جميع الطرق"""
        emergency_id = abs(hash(title)) % 10000
        emergency_url = f"https://football-news-emergency.blogspot.com/emergency-{emergency_id}.html"
        
        logger.warning(f"⚠️ Using emergency URL: {emergency_url}")
        return emergency_url
    
    def get_backup_stats(self):
        """الحصول على إحصائيات النسخ الاحتياطية"""
        try:
            backup_dir = "backup_articles"
            
            if not os.path.exists(backup_dir):
                return {"local_files": 0, "archive_articles": 0}
            
            # عدد الملفات المحلية
            local_files = len([f for f in os.listdir(backup_dir) if f.endswith('.html')])
            
            # عدد المقالات في الأرشيف
            archive_file = os.path.join(backup_dir, "articles_archive.json")
            archive_articles = 0
            
            if os.path.exists(archive_file):
                try:
                    with open(archive_file, 'r', encoding='utf-8') as f:
                        articles = json.load(f)
                        archive_articles = len(articles)
                except:
                    archive_articles = 0
            
            return {
                "local_files": local_files,
                "archive_articles": archive_articles,
                "backup_dir": backup_dir
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get backup stats: {e}")
            return {"local_files": 0, "archive_articles": 0, "error": str(e)}

# إنشاء instance عام
fallback_publisher = FallbackPublisher()
