# 🚀 دليل التشغيل السريع - بوت التيليجرام

## 📋 ملخص سريع

تم تحويل وكيل أخبار كرة القدم إلى بوت تيليجرام يمكن التحكم به بالكامل مع الحفاظ على العمل التلقائي المستقل.

## ⚡ التشغيل السريع

### 1. إنشاء بوت تيليجرام

1. ابدأ محادثة مع [@BotFather](https://t.me/BotFather)
2. أرسل `/newbot`
3. اختر اسم للبوت: `Football News Control`
4. اختر معرف للبوت: `your_football_bot`
5. انسخ التوكن

### 2. إعداد ملف .env

أضف التوكن في ملف `.env`:

```env
TELEGRAM_CONTROL_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
```

### 3. تشغيل البوت

```bash
python run_telegram_bot.py
```

اختر الخيار `1` لتشغيل بوت التيليجرام

### 4. استخدام البوت

1. ابحث عن البوت في التيليجرام
2. ابدأ محادثة
3. أرسل `/start`
4. استخدم الأزرار للتحكم

## 🎮 أوامر التحكم

| الزر | الوظيفة |
|------|---------|
| 📊 حالة البوت | عرض حالة الوكيل |
| ▶️ تشغيل | بدء تشغيل الوكيل |
| ⏹️ إيقاف | إيقاف الوكيل |
| 📈 إحصائيات | عرض إحصائيات مفصلة |
| 🔄 تحديث | تحديث المعلومات |

## 🔧 طرق التشغيل

### بوت التيليجرام (موصى به)
```bash
python run_telegram_bot.py
# اختر الخيار 1
```

### الوضع التلقائي (الأصلي)
```bash
python main.py
```

### وضع الاختبار
```bash
python main.py --test
```

## 🛠️ استكشاف الأخطاء

### البوت لا يعمل
```bash
# تحقق من المتطلبات
pip install -r requirements.txt

# اختبار النظام
python test_telegram_control.py
```

### التوكن غير صحيح
- تأكد من نسخ التوكن كاملاً من BotFather
- تأكد من عدم وجود مسافات إضافية

### البوت لا يستجيب
- تأكد من أن البوت مفعل مع BotFather
- أعد تشغيل البوت

## 📱 مميزات البوت

✅ **تحكم كامل عن بُعد**
- تشغيل/إيقاف الوكيل
- مراقبة الحالة
- عرض الإحصائيات

✅ **عمل مستقل**
- يعمل كل 30 دقيقة تلقائياً
- لا يحتاج تدخل بشري
- يحفظ حالته

✅ **سهولة الاستخدام**
- واجهة بسيطة
- أزرار تفاعلية
- متاح لجميع المستخدمين

## 🎯 الخلاصة

الآن يمكنك:
- 🤖 التحكم في الوكيل عبر التيليجرام
- 📱 مراقبة الحالة من أي مكان
- ⚡ تشغيل/إيقاف الوكيل فوراً
- 📊 عرض الإحصائيات والتقارير

**استمتع بوكيل الأخبار الجديد!** 🚀

---

📚 للمزيد من التفاصيل: راجع `TELEGRAM_BOT_GUIDE.md`
