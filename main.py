#!/usr/bin/env python3
"""
Football News Bot - Main Module
بوت أخبار كرة القدم - الوحدة الرئيسية
"""

import time
import asyncio
import datetime
from utils.logger import logger
from database import database
from timing_config import (
    ARTICLES_PER_CYCLE, DELAY_BETWEEN_ARTICLES, CYCLE_INTERVAL,
    TODAY_INDICATORS, OLD_NEWS_INDICATORS
)
from system_health_checker import health_checker

try:
    from scraper import sky_news_scraper
    from scraper import kooora_scraper_simple as kooora_scraper
except ImportError as e:
    print(f"Could not import scrapers: {e}")
    sky_news_scraper = None
    kooora_scraper = None

try:
    from generator import content_generator, image_generator
except ImportError as e:
    print(f"Could not import generators: {e}")
    content_generator = None
    image_generator = None

try:
    from publisher import blogger_publisher, telegram_publisher
except ImportError as e:
    print(f"Could not import publishers: {e}")
    blogger_publisher = None
    telegram_publisher = None




def main_cycle():
    """Runs one full cycle of the news bot."""
    logger.info("========================================")
    logger.info("Starting new cycle...")

    # 0. Check system health first
    logger.info("🏥 Checking system health...")
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        health_ok = loop.run_until_complete(health_checker.run_full_health_check())
        loop.close()

        if not health_ok:
            logger.error("❌ System health check failed. Skipping this cycle.")
            # Send notification about health issues
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(health_checker.send_health_notification())
                loop.close()
            except Exception as e:
                logger.error(f"Failed to send health notification: {e}")
            return
        else:
            logger.info("✅ System health check passed. Proceeding with cycle.")
    except Exception as e:
        logger.error(f"❌ Health check error: {e}. Proceeding with caution.")

    # 1. Scrape news
    all_articles = []

    # Sky News Arabia
    try:
        sky_articles = sky_news_scraper.scrape_sky_news()
        if sky_articles:
            all_articles.extend(sky_articles)
            logger.info(f"✅ Sky News: Found {len(sky_articles)} articles")
        else:
            logger.warning("⚠️ Sky News: No articles found")
    except Exception as e:
        logger.error(f"❌ Sky News scraping failed: {e}")

    # Kooora
    try:
        kooora_articles = kooora_scraper.scrape_kooora()
        if kooora_articles:
            all_articles.extend(kooora_articles)
            logger.info(f"✅ Kooora: Found {len(kooora_articles)} articles")
        else:
            logger.warning("⚠️ Kooora: No articles found")
    except Exception as e:
        logger.error(f"❌ Kooora scraping failed: {e}")

    if not all_articles:
        logger.warning("⚠️ No articles found from any source in this cycle!")
        return

    logger.info(f"📰 Total articles found: {len(all_articles)}")

    # 2. Process articles with timing control
    processed_count = 0
    max_articles_per_cycle = ARTICLES_PER_CYCLE

    # Filter for today's news only
    today_articles = []

    for article in all_articles:
        # Check if article is from today (basic check)
        title = article['title'].lower()
        # Skip articles that seem old
        if any(indicator in title for indicator in OLD_NEWS_INDICATORS):
            logger.info(f"Skipping old article: {article['title'][:50]}...")
            continue
        today_articles.append(article)

    if not today_articles:
        logger.warning("No recent articles found for today!")
        return

    logger.info(f"Found {len(today_articles)} recent articles for processing")

    for article_summary in today_articles:
        # Stop if we've processed enough articles for this cycle
        if processed_count >= max_articles_per_cycle:
            logger.info(f"⏰ Reached limit of {max_articles_per_cycle} article per cycle. Next article will be processed in 30 minutes.")
            break

        title = article_summary['title']
        url = article_summary['url']
        source = article_summary['source']

        # 2a. Check if article already exists
        if database.article_exists(url):
            logger.info(f"Article '{title}' already exists. Skipping.")
            continue

        logger.info(f"Processing new article: '{title}'")

        # 2b. Generate content with improved title and internal linking
        result = content_generator.generate_article(title, source)
        if len(result) == 4:
            article_content, keywords, catchy_title, entities = result
        else:
            # Fallback for backward compatibility
            article_content, keywords, catchy_title = result
            entities = []

        if not article_content:
            logger.error(f"Failed to generate content for '{title}'. Skipping.")
            continue

        logger.info(f"Using catchy title for publishing: '{catchy_title}'")

        # 2d. Generate smart image with AI-powered search and analysis
        image_path = image_generator.generate_post_image(catchy_title, "imgs", article_content, use_smart_generation=True)
        if not image_path:
            logger.warning(f"Failed to generate image for '{catchy_title}'. Proceeding without image.")

        # 2e. Check Blogger connection before publishing
        logger.info("🔍 Checking Blogger connection before publishing...")
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            blogger_ok = loop.run_until_complete(health_checker.check_blogger_connection())
            loop.close()

            if not blogger_ok:
                logger.error(f"❌ Blogger connection failed. Skipping publication of '{catchy_title}'")
                continue
        except Exception as e:
            logger.error(f"❌ Blogger connection check error: {e}. Attempting to publish anyway.")

        # 2f. Publish to Blogger
        post_url = blogger_publisher.publish_to_blogger(catchy_title, article_content, keywords, image_path)
        if not post_url:
            logger.error(f"Failed to publish '{catchy_title}' to Blogger. Skipping.")
            continue

        # 2f. Add to database
        database.add_news_article(catchy_title, url, source)
        logger.info(f"Successfully added '{catchy_title}' to the database.")

        # 2f2. Add published article and entities to database for internal linking
        try:
            # Get the original news article ID
            original_news_id = database.get_news_article_id(url)

            # Create content preview (first 200 characters)
            content_preview = article_content[:200] + "..." if len(article_content) > 200 else article_content

            # Add published article
            published_article_id = database.add_published_article(
                catchy_title, post_url, original_news_id, keywords, content_preview
            )

            if published_article_id and entities:
                # Convert entities to the format expected by database
                entity_list = []
                for player in entities.get('players', []):
                    entity_list.append({'name': player, 'type': 'player'})
                for team in entities.get('teams', []):
                    entity_list.append({'name': team, 'type': 'team'})
                for comp in entities.get('competitions', []):
                    entity_list.append({'name': comp, 'type': 'competition'})

                # Add entities to database
                database.add_article_entities(published_article_id, entity_list)
                logger.info(f"Added {len(entity_list)} entities for article '{catchy_title}'")

        except Exception as e:
            logger.error(f"Failed to save published article data: {e}")

        # 2g. Send Telegram notification
        try:
            asyncio.run(telegram_publisher.send_telegram_notification(catchy_title, post_url))
        except Exception as e:
            logger.error(f"Failed to send Telegram notification for '{catchy_title}': {e}")

        # Increment processed count
        processed_count += 1
        logger.info(f"✅ Article {processed_count}/{max_articles_per_cycle} processed successfully")

        # Wait before processing next article (if any)
        if processed_count < max_articles_per_cycle and processed_count < len(today_articles):
            logger.info(f"⏰ Waiting {DELAY_BETWEEN_ARTICLES//60} minutes before processing next article...")
            time.sleep(DELAY_BETWEEN_ARTICLES)
    
    logger.info("Cycle finished.")
    logger.info("========================================\n")

def run_autonomous_mode():
    """Run the bot in autonomous mode (original behavior)"""
    # Initialize the database
    database.init_db()
    logger.info("Database initialized.")

    # Main loop
    while True:
        try:
            main_cycle()
            # Wait before the next cycle
            logger.info(f"Sleeping for {CYCLE_INTERVAL//60} minutes...")
            time.sleep(CYCLE_INTERVAL)
        except KeyboardInterrupt:
            logger.info("Bot stopped manually.")
            break
        except Exception as e:
            logger.critical(f"A critical error occurred in the main loop: {e}")
            # Wait for 5 minutes before retrying to avoid spamming errors
            time.sleep(300)

if __name__ == '__main__':
    # Initialize the database
    database.init_db()
    logger.info("Database initialized.")

    # Check if we're running in test mode
    import sys
    test_mode = '--test' in sys.argv
    telegram_mode = '--telegram' in sys.argv

    if test_mode:
        logger.info("Running in test mode - single cycle only")
        try:
            main_cycle()
            logger.info("Test cycle completed successfully!")
        except Exception as e:
            logger.critical(f"Test cycle failed: {e}")
    elif telegram_mode:
        logger.info("Starting Telegram control bot...")
        try:
            from telegram_control_bot import main as telegram_main
            telegram_main()
        except ImportError:
            logger.error("Telegram control bot not available. Please ensure telegram_control_bot.py exists.")
        except Exception as e:
            logger.critical(f"Error starting Telegram control bot: {e}")
    else:
        # Original autonomous mode
        run_autonomous_mode()
